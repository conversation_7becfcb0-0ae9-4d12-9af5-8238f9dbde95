import { getRequestURL, sendRedirect, sendError } from "h3"
import { getPathWithoutLocale } from "@/utils/localeUtils"
import { processHtmlContent, getBaseUrl } from "@/utils/dataUtils";
import { stringify } from "qs"
import { ReasonPhrases as RP, StatusCodes as SC } from "http-status-codes"
import type { FetchError } from "ofetch"
const { FISHBUSH_API_KEY } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
  const url = getRequestURL(event)
  const urlPath = url.pathname

  // Skip processing for API and static files
  if (
    urlPath.startsWith("/api") ||
    urlPath.startsWith("/_nuxt") ||
    urlPath.startsWith("/__nuxt") ||
    urlPath.startsWith("/uploads")
  ) {
    return
  }

  // Get config
  const config = useRuntimeConfig()
  const defaultLocale = config.DEFAULT_LOCALE

  // Extract locale from URL path if present
  // Match patterns like /en/... or /cs/...
  const localeRegex = /^\/([a-z]{2})(\/|$)/
  const localeMatch = urlPath.match(localeRegex)
  const locale = localeMatch ? localeMatch[1] : defaultLocale

  // Get path without locale prefix
  const cleanedPath = getPathWithoutLocale(urlPath, locale, defaultLocale)

  const modifiers: FBQueryParams = {
    slug: cleanedPath,
    locale: locale,
  }

  const queryString = stringify(modifiers, { encodeValuesOnly: true })
  const baseUrl = getBaseUrl();
  const fullUrl = new URL("api/web", baseUrl);
  fullUrl.search = `?${queryString}`;

  // now call $fetch
  try {
    const res = await $fetch<WebPage>(fullUrl.href, {
      headers: {
        Authorization: `Bearer ${FISHBUSH_API_KEY}`,
      },
    })

    // No blocks indicates either redirect or 404
    if (!res.blocks) {
      return sendError(
        event,
        createError({
          statusCode: SC.INTERNAL_SERVER_ERROR,
          statusMessage: RP.INTERNAL_SERVER_ERROR,
        })
      )
    }

    if (res.blocks.length === 0) {
      if (res.noContentRedirectUrl) {
        console.log(`Redirecting to ${res.noContentRedirectUrl}`)
        return sendRedirect(
          event,
          res.noContentRedirectUrl,
          res.redirectType === Enum_Webpage_Redirecttype.Permanent
            ? SC.PERMANENT_REDIRECT
            : SC.TEMPORARY_REDIRECT
        )
      }

      return sendError(
        event,
        createError({ statusCode: SC.NOT_FOUND, statusMessage: RP.NOT_FOUND })
      )
    }

    // biome-ignore lint/suspicious/noExplicitAny: No Error is possible on this level
    for (const block of res.blocks as any as ContentBlock[]) {
      if (!block.content) continue
      block.content = processHtmlContent(block.content, locale)
    }

    /**
     * To avoid refetching in the page component, we store the data in the event context
     * This is then read in [...slug].vue and prevents the need for useAsyncData
     */
    console.info("Setting webPageData in event context")
    event.context.webPageData = res
  } catch (error) {
    const fetchError = error as FetchError
    return sendError(
      event,
      createError({
        statusCode: fetchError.statusCode,
        statusMessage: fetchError.statusMessage,
      })
    )
  }
})

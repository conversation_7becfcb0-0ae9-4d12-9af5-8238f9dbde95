// /server/api/web-pages.ts
import { createError, defineEventHand<PERSON>, sendRedirect, type H3Event } from "h3";
import { stringify } from "qs";
import { Enum_Webpage_Redirecttype } from "@/models/types";
import { ReasonPhrases as RP,	StatusCodes as SC } from 'http-status-codes';
import { processHtmlContent, getBaseUrl } from "@/utils/dataUtils";
const { FISHBUSH_API_KEY, DEFAULT_LOCALE } = useRuntimeConfig();

export default defineEventHandler(async (event) => {

	const baseUrl = getBaseUrl();

	const fullUrl = new URL("api/web", baseUrl);

  // Get query parameters from the incoming request
  const incomingQuery = getQuery(event);

  // Extract the locale from the query parameters, default to "cs" if not provided
  const locale = incomingQuery.locale?.toString() || DEFAULT_LOCALE;

	// Extract the slug from the query parameters
	const slug = incomingQuery.slug?.toString();

  // if slug is not provided, return 404
	if (!slug) {
		return sendError(event, createError({ statusCode: SC.NOT_FOUND, statusMessage: RP.NOT_FOUND }));
	}

	const queryParams: FBQueryParams = {
		slug: slug,
		locale: locale
	};

	// Serialize query parameters
	const queryString = stringify(queryParams, { encodeValuesOnly: true });
	fullUrl.search = `?${queryString}`;

  try {
    const response = await fetch(fullUrl.href, {
      headers: {
        Authorization: `Bearer ${FISHBUSH_API_KEY}`,
      },
    });

    if (!response.ok) {
      return sendError(event, createError({ statusCode: SC.NOT_FOUND, statusMessage: RP.NOT_FOUND }));
    }

    const json = await response.json();

		return processWebPage(json, locale, event);

  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] ${error}`);
    return sendError(event, createError({ statusCode: SC.INTERNAL_SERVER_ERROR, statusMessage: RP.INTERNAL_SERVER_ERROR }));
  }
});

// Helper function to process page data
function processWebPage(json: WebPage, locale: string, event: H3Event) {
  // No blocks indicates either redirect or 404
  if (!json.blocks) {
    return sendError(event, createError({ statusCode: SC.INTERNAL_SERVER_ERROR, statusMessage: RP.INTERNAL_SERVER_ERROR }));
  }
  
  if (json.blocks.length === 0) {
    if (json.noContentRedirectUrl) {
      console.log(`Redirecting to ${json.noContentRedirectUrl}`);
      return sendRedirect(
        event,
        json.noContentRedirectUrl,
        json.redirectType === Enum_Webpage_Redirecttype.Permanent ? SC.PERMANENT_REDIRECT : SC.TEMPORARY_REDIRECT
      );
    }

    return sendError(event, createError({ statusCode: SC.NOT_FOUND, statusMessage: RP.NOT_FOUND }));
  }


	// biome-ignore lint/suspicious/noExplicitAny: No Error is possible on this level
	for (const block of json.blocks as any as ContentBlock[]) {
    if (!block.content) continue;
    block.content = processHtmlContent(block.content, locale);
  }

  return json;
}
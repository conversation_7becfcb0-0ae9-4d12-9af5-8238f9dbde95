<script setup lang="ts">
import type { FBQueryParams } from "~/models/FishBushCustomTypes";
import { useFishBushStore } from "@/stores/FishBush";
import { useHead } from "@vueuse/head";
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';
import { getPathWithoutLocale } from "@/utils/localeUtils";

// Type for Strapi 5 block structure
interface CmsBlock {
	id: number;
	__component: string;
	publishDate?: string;
	unpublishDate?: string;
	content?: string;
	[key: string]: any; // For component-specific properties like picture, etc.
}

interface CmsPage {
	id: number;
	documentId: string;
	slug: string;
	title: string;
	metaDescription?: string;
	blocks?: CmsBlock[];
	[key: string]: any;
}

const route = useRoute();
const router = useRouter();
const FBstore = useFishBushStore();
const resource = "web-pages";
const { locale, defaultLocale, t } = useI18n();

const cleanedPath = getPathWithoutLocale(route.path, locale.value, defaultLocale);

const modifiers: FBQueryParams = {
	slug: cleanedPath,
	locale: locale.value
};

const event = useRequestEvent();
const cmsPage = event?.context?.webPageData as CmsPage | undefined;

// Set the page title and omit '|' if there is no title
useHead(() => {
	const title = cmsPage?.title ? `${cmsPage.title} | Tour de App` : t('meta.title')
	const description = cmsPage?.metaDescription ?? t('meta.description')
	const image = t('meta.image')
	return ({
	title: title,
	meta: [
		{ name: "google-site-verification", content: "SpVcESVrPL3wu7JhdAh0LfqCrDnXfMHj7N7hpJRTeiQ" },
		{ name: "description", content: description	},
    { property: 'og:title', content: title },
    { property: 'og:description', content: description },
		{ property: 'og:image', content: image },
		{ property: 'og:site_name', content: 'Tour de App' },
		{ name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
		{ name: 'twitter:image', content: image },
		{ name: 'theme-color', content: '#EF8A17' },
	]
})});

const validBlocks = computed(() => {
	if (!cmsPage?.blocks) return [];

	const blocks = cmsPage.blocks.filter((block: CmsBlock) => {
		if (!block || !block.__component) return false;

		// Check if block should be published based on publishDate and unpublishDate
		const now = new Date();
		const publishDate = block.publishDate ? new Date(block.publishDate) : null;
		const unpublishDate = block.unpublishDate ? new Date(block.unpublishDate) : null;

		// Block should be published if:
		// - No publishDate is set, OR publishDate is in the past
		// AND
		// - No unpublishDate is set, OR unpublishDate is in the future
		const isPublished = (!publishDate || publishDate <= now) && (!unpublishDate || unpublishDate > now);

		return isPublished;
	});

	return blocks;
});

const getMetaProps = (block: CmsBlock) => {
	// Extract metadata properties, excluding content and __component
	const { content, __component, ...metaProps } = block;
	return metaProps;
};

const componentName = (componentString: string) => {
	const parts = componentString.split(".");
	if (parts.length > 1) {
		const nameParts = parts[1]
			.split("-")
			.map((part) => part.charAt(0).toUpperCase() + part.slice(1))
			.join("");
		return `ContentBlocks${nameParts}`;
	}
	return "";
};
</script>

<template>
  <div v-if="cmsPage">
    <div v-for="block in validBlocks" :key="block.id">
      <component :is="componentName(block.__component)" :content="block.content ?? ''" :meta="getMetaProps(block)" />
    </div>
  </div>
  <!-- <Error404 v-else-if="error?.statusCode === 404" />
  <Error500 v-else-if="error" :code='error?.statusCode'/> -->
  <LoadingSpinner v-else />
</template>

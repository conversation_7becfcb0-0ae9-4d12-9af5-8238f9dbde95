<script setup lang="ts">
import type { FBQueryParams } from "~/models/FishBushCustomTypes";
import type { WebPageBlocksDynamicZone } from "@/models/types";
import { useFishBushStore } from "@/stores/FishBush";
import { useHead } from "@vueuse/head";
import { computed, watchEffect } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';
import { getPathWithoutLocale } from "@/utils/localeUtils";

const route = useRoute();
const router = useRouter();
const FBstore = useFishBushStore();
const resource = "web-pages";
const { locale, defaultLocale, t } = useI18n();

const cleanedPath = getPathWithoutLocale(route.path, locale.value, defaultLocale);

const modifiers: FBQueryParams = {
	slug: cleanedPath,
	locale: locale.value
};

const event = useRequestEvent();
const cmsPage = event?.context?.webPageData;

// const { data: webPageData, error, status } = await useAsyncData(
// 	resource + route.path,
// 	async () => {
// 		try {
// 			const result = await FBstore.fetchFishBushData(resource, modifiers);
// 			// console.log(result);
// 			// the result is either an array of data (webpage) or an object with redirectUrl key, like { redirectUrl: '/o-nas' }
// 			return result;
// 		} catch (err) {
// 			throw createError({ statusCode: typeof err === 'number' ? err : 500, message: "Error thrown" });
// 		}
// 	},
// );

// const webPageBlocks = computed(() => {
// 	return (webPageData.value ?? [])
// });

// Set the page title and omit '|' if there is no title
// useHead(() => { 
// 	const title = webPage.value?.attributes?.title ? `${webPage.value?.attributes?.title} | Tour de App` : t('meta.title')
// 	const description = webPage.value?.attributes?.metaDescription ?? t('meta.description')
// 	const image = t('meta.image')
// 	return ({
// 	title: title,
// 	meta: [
// 		{ name: "google-site-verification", content: "SpVcESVrPL3wu7JhdAh0LfqCrDnXfMHj7N7hpJRTeiQ" },
// 		{ name: "description", content: description	},
//     { property: 'og:title', content: title },
//     { property: 'og:description', content: description },
// 		{ property: 'og:image', content: image },
// 		{ property: 'og:site_name', content: 'Tour de App' },
// 		{ name: 'twitter:card', content: 'summary_large_image' },
//     { name: 'twitter:title', content: title },
//     { name: 'twitter:description', content: description },
// 		{ name: 'twitter:image', content: image },
// 		{ name: 'theme-color', content: '#EF8A17' },
// 	]
// })});

// const validBlocks = computed(() => {
// 	const blocks =
// 		(webPage.value?.attributes?.blocks as (WebPageBlocksDynamicZone & {
// 			id: number;
// 			__component: string;
// 			publishDate: string;
// 			unpublishDate?: string;
// 			content?: string;
// 		})[]) || [];
// 		return blocks
// });

const getMetaProps = (block: Exclude<WebPageBlocksDynamicZone, Error>) => {
	// Type guard to ensure block has content and __component properties
	if ('content' in block && '__component' in block) {
		const { content, __component, ...metaProps } = block;
		return metaProps;
	}
	// If the properties don't exist, return an empty object or handle the case as needed
	return {};
};

const componentName = (componentString: string) => {
	const parts = componentString.split(".");
	if (parts.length > 1) {
		const nameParts = parts[1]
			.split("-")
			.map((part) => part.charAt(0).toUpperCase() + part.slice(1))
			.join("");
		return `ContentBlocks${nameParts}`;
	}
	return "";
};
</script>

<template>
	<!-- <div>{{ webPageData }}</div> 
	<div>{{ error }}</div> -->
	<div>{{ cmsPage }}</div>
	<!-- <div>{{ status }}</div> -->
  <div v-if="cmsPage">
    <!-- <div v-for="block in validBlocks" :key="block.id">
      <component :is="componentName(block.__component)" :content="block.content ?? ''" :meta="getMetaProps(block)" />
    </div> -->
  </div>
  <!-- <Error404 v-else-if="error?.statusCode === 404" />
  <Error500 v-else-if="error" :code='error?.statusCode'/> -->
  <LoadingSpinner v-else />
</template>
